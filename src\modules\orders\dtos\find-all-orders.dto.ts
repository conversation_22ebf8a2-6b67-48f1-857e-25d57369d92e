import { IOrderDto } from "./order.dto";

export interface IFindAllOrdersRequest {
	data: IOrderDto[];
	total: number;
}

export interface IFindAllOrdersParams {
	page: number;
	limit: number;
	orderId?: string;
	customer?: string;
	orderStatus?: number;
}

export interface IOrderDetailedDto {
	id: number;
	status: string;
	subtotal: string;
	shippingCost: string;
	discount: string;
	total: string;
	salesperson: string;
	items: IOrderItemDto[];
	payments: IOrderPaymentDto[];
	customer: string;
	customerCpfCnpj: string;
	invoice: IOrderInvoiceDto[];
}

export interface IOrderItemDto {
	id: number;
	quantity: number;
	price: number;
	discount: number;
	total: string;
	product: string;
}

export interface IOrderPaymentDto {
	id: number;
	value: string;
	paymentMethod: string;
	parcel: number;
}

export interface IOrderInvoiceDto {
	id: number;
	type: number;
	status: string;
	issueDate: string;
	key: string;
	sequence: number;
	orderId: number;
	supplier: string;
	events: IOrderInvoiceEventDto[];
}

export interface IOrderInvoiceEventDto {
	id: string;
	date: string;
	reason: string;
}
