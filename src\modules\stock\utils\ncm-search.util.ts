import { INcmData, INcmItem, INcmSearchResult } from "../types/ncm.types";

export class NcmSearchUtil {
	private static instance: NcmSearchUtil;
	private ncmData: INcmItem[] | null = null;
	private isLoading = false;

	private constructor() {}

	public static getInstance(): NcmSearchUtil {
		if (!NcmSearchUtil.instance) {
			NcmSearchUtil.instance = new NcmSearchUtil();
		}
		return NcmSearchUtil.instance;
	}

	public async loadNcmData(): Promise<void> {
		if (this.ncmData || this.isLoading) return;

		this.isLoading = true;
		try {
			// Import dinâmico do JSON
			const module = await import("../data/ncm.json");
			const data = module.default as INcmData;
			this.ncmData = data.Nomenclaturas;
		} catch (error) {
			console.error("Erro ao carregar dados NCM:", error);
			this.ncmData = [];
		} finally {
			this.isLoading = false;
		}
	}

	public searchNcm(searchTerm: string, limit = 50): INcmSearchResult[] {
		if (!this.ncmData || !searchTerm.trim()) {
			return [];
		}

		const normalizedSearch = this.normalizeString(searchTerm.toLowerCase());
		const results: INcmSearchResult[] = [];

		// Busca por código exato primeiro
		const exactCodeMatches = this.ncmData.filter(item => item.Codigo.toLowerCase().includes(searchTerm.toLowerCase()));

		// Busca por descrição
		const descriptionMatches = this.ncmData.filter(
			item =>
				this.normalizeString(item.Descricao.toLowerCase()).includes(normalizedSearch) &&
				!exactCodeMatches.some(exact => exact.Codigo === item.Codigo)
		);

		// Combina resultados priorizando códigos
		const combinedResults = [...exactCodeMatches, ...descriptionMatches];

		for (const item of combinedResults) {
			if (results.length >= limit) break;

			results.push({
				codigo: item.Codigo,
				descricao: item.Descricao,
				displayText: `${item.Codigo} - ${item.Descricao}`,
			});
		}

		return results;
	}

	public findExactNcm(codigo: string): INcmSearchResult | null {
		if (!this.ncmData || !codigo.trim()) {
			return null;
		}

		const found = this.ncmData.find(item => item.Codigo === codigo);
		if (found) {
			return {
				codigo: found.Codigo,
				descricao: found.Descricao,
				displayText: `${found.Codigo} - ${found.Descricao}`,
			};
		}

		return null;
	}

	private normalizeString(str: string): string {
		return str
			.normalize("NFD")
			.replace(/[\u0300-\u036f]/g, "")
			.toLowerCase();
	}

	public isDataLoaded(): boolean {
		return this.ncmData !== null;
	}

	public getDataLength(): number {
		return this.ncmData?.length || 0;
	}
}

export const ncmSearchUtil = NcmSearchUtil.getInstance();
